"""
OSMap - Robot Navigation System for Robotour Competition

A comprehensive navigation system for autonomous robots using OpenStreetMap data.
Designed specifically for the Robotour competition requirements.

Main Components:
- MapLoader: Load and process OSM data
- PathPlanner: Calculate shortest paths
- NavigationController: Main navigation API
- XMLStorage: Serialize/deserialize maps
- DirectionCalculator: Calculate movement directions
- ProgressTracker: Track navigation progress
- Logger: Comprehensive logging system

Author: OSMap Development Team
Version: 1.0.0
License: MIT
"""

from .navigation import NavigationController
from .map_loader import MapLoader
from .path_planner import PathPlanner
from .xml_storage import XMLStorage
from .direction import DirectionCalculator
from .progress import ProgressTracker
from .logger import OSMapLogger
from .data_structures import RoadNetwork, Path, Direction, NavigationStatus

__version__ = "1.0.0"
__author__ = "OSMap Development Team"

__all__ = [
    "NavigationController",
    "MapLoader", 
    "PathPlanner",
    "XMLStorage",
    "DirectionCalculator",
    "ProgressTracker",
    "OSMapLogger",
    "RoadNetwork",
    "Path", 
    "Direction",
    "NavigationStatus"
]
