# OSMap - Robot Navigation System Architecture

## Overview
OSMap je navigačný systém pre autonómne roboty navrhnutý pre súťaž Robotour. Systém využíva OpenStreetMap dáta pre navigáciu po cestách a poskytuje real-time navigačné pokyny.

## Požiadavky z Robotour pravidiel
- Navigácia len po cestách (paved passage ways)
- Použitie OpenStreetMap dát
- Plne autonómna navigácia
- Práca s GPS súradnicami
- Časový limit 1 hodina, vzdialenosť až 1km
- Detekcia odchýlky od trasy

## Hlavné komponenty systému

### 1. MapLoader (`osmap/map_loader.py`)
**Účel**: Načítanie a spracovanie OSM dát
**Funkcie**:
- Stiahnutie cestnej siete z OSM pomocou OSMnx
- Filtrovanie relevantných ciest pre robota
- Konverzia do internej reprezentácie
- Cache management pre offline použitie

**API**:
```python
class MapLoader:
    def load_area_by_bbox(bbox: tuple) -> RoadNetwork
    def load_area_by_center(center: tuple, radius: float) -> RoadNetwork
    def filter_robot_roads(network: RoadNetwork) -> RoadNetwork
    def save_to_cache(network: RoadNetwork, filepath: str)
    def load_from_cache(filepath: str) -> RoadNetwork
```

### 2. XMLStorage (`osmap/xml_storage.py`)
**Účel**: Serializácia/deserializácia mapy do XML formátu
**Funkcie**:
- Export/import cestnej siete do XML
- Validácia XML schémy
- Kompresné algoritmy pre veľké mapy

**XML Schéma**:
```xml
<osmap version="1.0">
    <metadata>
        <bounds minlat="..." maxlat="..." minlon="..." maxlon="..."/>
        <created_at>...</created_at>
    </metadata>
    <nodes>
        <node id="..." lat="..." lon="..." />
    </nodes>
    <ways>
        <way id="..." highway="..." maxspeed="...">
            <nd ref="..."/>
            <nd ref="..."/>
        </way>
    </ways>
</osmap>
```

### 3. PathPlanner (`osmap/path_planner.py`)
**Účel**: Výpočet najkratšej cesty medzi bodmi
**Funkcie**:
- A* algoritmus pre pathfinding
- Mapovanie GPS súradníc na uzly grafu
- Optimalizácia trasy pre robota

**API**:
```python
class PathPlanner:
    def __init__(self, road_network: RoadNetwork)
    def find_shortest_path(start_gps: tuple, end_gps: tuple) -> Path
    def find_nearest_node(gps_coord: tuple) -> int
    def calculate_path_distance(path: Path) -> float
    def get_path_waypoints(path: Path) -> List[tuple]
```

### 4. NavigationController (`osmap/navigation.py`)
**Účel**: Hlavná navigačná knižnica - API pre robota
**Funkcie**:
- Inicializácia navigácie
- Real-time navigačné pokyny
- Sledovanie pokroku
- Detekcia odchýlky od trasy

**API**:
```python
class NavigationController:
    def initialize(self, current_gps: tuple, target_gps: tuple) -> bool
    def update_position(self, current_gps: tuple) -> NavigationStatus
    def get_direction(self) -> Direction
    def get_distance_to_target(self) -> float
    def is_on_track(self) -> bool
```

**NavigationStatus**:
- `OK`: Robot ide správnym smerom
- `WRONG`: Robot sa vzďaľuje od cieľa
- `STATIC`: Robot sa nepohybuje
- `ARRIVED`: Robot dosiahol cieľ

### 5. DirectionCalculator (`osmap/direction.py`)
**Účel**: Výpočet smeru pohybu robota
**Funkcie**:
- Výpočet bearingu medzi bodmi
- Určenie smeru otočenia
- Detekcia odchýlky od trasy

### 6. ProgressTracker (`osmap/progress.py`)
**Účel**: Sledovanie pokroku navigácie
**Funkcie**:
- Porovnávanie vzdialeností
- Detekcia približovania/vzďaľovania
- História pozícií

### 7. Logger (`osmap/logger.py`)
**Účel**: Komplexný logging systém
**Funkcie**:
- Zaznamenávanie všetkých operácií
- Štruktúrované logy pre debugging
- Export logov pre analýzu

## Dátové štruktúry

### RoadNetwork
```python
@dataclass
class RoadNetwork:
    graph: nx.MultiDiGraph
    bounds: BoundingBox
    metadata: Dict[str, Any]
    nodes: Dict[int, Node]
    ways: Dict[int, Way]
```

### Path
```python
@dataclass
class Path:
    waypoints: List[tuple]  # GPS súradnice
    nodes: List[int]        # ID uzlov v grafe
    total_distance: float
    estimated_time: float
```

### Direction
```python
@dataclass
class Direction:
    bearing: float          # Smer v stupňoch (0-360)
    distance: float         # Vzdialenosť k ďalšiemu bodu
    instruction: str        # Textový popis ("turn left", "go straight")
    next_waypoint: tuple    # GPS súradnice ďalšieho bodu
```

## Workflow navigácie

1. **Inicializácia**:
   - Načítanie mapy z XML súboru
   - Vytvorenie PathPlanner s cestnou sieťou
   - Inicializácia NavigationController

2. **Plánovanie trasy**:
   - Mapovanie GPS súradníc na uzly grafu
   - Výpočet najkratšej cesty pomocou A*
   - Vytvorenie zoznamu waypoints

3. **Navigácia**:
   - Priebežné volanie update_position()
   - Výpočet smeru k ďalšiemu waypoint
   - Sledovanie pokroku a detekcia odchýlky
   - Logging všetkých operácií

## Integrácia s robotom

Knižnica poskytuje jednoduché API pre integráciu:

```python
from osmap import NavigationController

# Inicializácia
nav = NavigationController("map.xml")
nav.initialize(current_gps=(48.123, 16.456), target_gps=(48.789, 16.012))

# V hlavnej slučke robota
while True:
    current_pos = robot.get_gps_position()
    status = nav.update_position(current_pos)
    
    if status.state == "OK":
        direction = nav.get_direction()
        robot.move_towards(direction.bearing)
    elif status.state == "WRONG":
        robot.stop()
        # Prepočítaj trasu alebo vráť sa na trasu
    elif status.state == "ARRIVED":
        robot.stop()
        break
```

## Technické požiadavky

- Python 3.8+
- OSMnx knižnica pre OSM dáta
- NetworkX pre graf algoritmy
- Shapely pre geometrické operácie
- XML parsing knižnice
- Logging framework
