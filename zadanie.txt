(https://github.com/robotika/robotour/blob/ROBOTOUR2023RC1/rules/rules.md), pozri si aj inde, ako sa riesi v pythone roboorienting problem.
1. mapa sa zameriava na cesty, aby sa mohla vytvorit navigacia pre robota po tychto cestach
2. mapa nebude velka, predpolada sa plocha okolo 4km stvorovych
3. mapa bude pracovat s dvoma polohami: 
- aktualna GPS suradnica
- cielova GPS suradnica
4. Po zadani cielovej GPS suradnice ju system urci na mape a dokaze vygenerovat najkratsiu cestu po cestach na mape k cielu
5/ vystupomo bude kniznica, ktora bude mat inicacnu cast, kde sa jej poslu aktualna a cielova suradnica). Dalsou castou bude priebezna cast, kde sa stale budu posielat obe suradnice (aktual a ciel). Kniznica bude vracat smer, ktorym sa ma robot pohybovat. V pripade priblizenia sa k cielu (cesta sa skrati) sa bude vracat ok stav, ze robot ide spravnym smerom (kniznica bude porovnavat predosly odoslany stav k aktualnemu), alego wrong stav, ak sa robot vzdaluje, pripadne static stav, ak sa poloha nemeni.
6. kniznica musi by lahko importovatelna do hlavneho programu robota, kde sa bude volat v slucke.
7. sucastou je samozrejme inicializacny podprogram, ktorym sa nacita samotna mapa a vytvori sa vektorova mapa ciest, ktora bude ulozena v spominanych xml suboroch, z ktorych, resp. z ktoreho bude kniznica citat pocas jazdy robota
8. sprav k tomu aj demo program, ktory kniznicu bude pouzivat a bude jasne zobrazovat mapu, polohu robota a oznaceny ciel, aby som to mohol potom debugovat
9. vsetko zdokumentuj a priloz do suborov v zlozke
10. vsetko dokladne loguj pocas jazdy robota
